('G:\\project\\msbuildtool\\build\\build\\PYZ-00.pyz',
 [('_compat_pickle', 'G:\\data\\anaconda3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'G:\\data\\anaconda3\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'G:\\data\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'G:\\data\\anaconda3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'G:\\data\\anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'G:\\data\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'G:\\data\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'G:\\data\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'G:\\data\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'G:\\data\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'G:\\data\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'G:\\data\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'G:\\data\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'G:\\data\\anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'G:\\data\\anaconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'G:\\data\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'G:\\data\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'G:\\data\\anaconda3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'G:\\data\\anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses', 'G:\\data\\anaconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'G:\\data\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'G:\\data\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'G:\\data\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'G:\\data\\anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'G:\\data\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'G:\\data\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'G:\\data\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'G:\\data\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'G:\\data\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'G:\\data\\anaconda3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'G:\\data\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'G:\\data\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'G:\\data\\anaconda3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'G:\\data\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'G:\\data\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'G:\\data\\anaconda3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'G:\\data\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'G:\\data\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'G:\\data\\anaconda3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'G:\\data\\anaconda3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'G:\\data\\anaconda3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'G:\\data\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'G:\\data\\anaconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'G:\\data\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'G:\\data\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'G:\\data\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'G:\\data\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'G:\\data\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'G:\\data\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'G:\\data\\anaconda3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc',
   'G:\\data\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'G:\\data\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'G:\\data\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'G:\\data\\anaconda3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'G:\\data\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'G:\\data\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'G:\\data\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'G:\\data\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'G:\\data\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'G:\\data\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'G:\\data\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'G:\\data\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'G:\\data\\anaconda3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'G:\\data\\anaconda3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'G:\\data\\anaconda3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'G:\\data\\anaconda3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'G:\\data\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'G:\\data\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'G:\\data\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'G:\\data\\anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'G:\\data\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'G:\\data\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'G:\\data\\anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'G:\\data\\anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'G:\\data\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'G:\\data\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'G:\\data\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'G:\\data\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'G:\\data\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'G:\\data\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'G:\\data\\anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'G:\\data\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'G:\\data\\anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'G:\\data\\anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'G:\\data\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'G:\\data\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'G:\\data\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'G:\\data\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'G:\\data\\anaconda3\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'G:\\data\\anaconda3\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'G:\\data\\anaconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'G:\\data\\anaconda3\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'G:\\data\\anaconda3\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'G:\\data\\anaconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'G:\\data\\anaconda3\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'G:\\data\\anaconda3\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'G:\\data\\anaconda3\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'G:\\data\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'G:\\data\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'G:\\data\\anaconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'G:\\data\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'G:\\data\\anaconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'G:\\data\\anaconda3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('zipfile', 'G:\\data\\anaconda3\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'G:\\data\\anaconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'G:\\data\\anaconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
