#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动构建脚本
自动增加版本号并构建项目
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime
from version_manager import VersionManager


def run_command(command: str, description: str = "") -> bool:
    """运行命令并显示结果"""
    if description:
        print(f"\n{description}")
        print("-" * 50)
    
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查必要文件
    required_files = ['msbuild.py', 'build.spec']
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} 存在")
        else:
            print(f"✗ {file} 不存在")
            return False
    
    return True


def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist/__pycache__']
    
    for dir_path in dirs_to_clean:
        if os.path.exists(dir_path):
            print(f"清理目录: {dir_path}")
            if os.name == 'nt':  # Windows
                run_command(f'rmdir /s /q "{dir_path}"', "")
            else:  # Unix/Linux
                run_command(f'rm -rf "{dir_path}"', "")


def backup_old_version():
    """备份旧版本"""
    dist_dir = "dist"
    if os.path.exists(dist_dir):
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"备份旧版本到: {backup_dir}")
        
        if os.name == 'nt':  # Windows
            run_command(f'xcopy "{dist_dir}" "{backup_dir}" /E /I /Q', "")
        else:  # Unix/Linux
            run_command(f'cp -r "{dist_dir}" "{backup_dir}"', "")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自动构建脚本')
    parser.add_argument('--type', choices=['major', 'minor', 'patch'], 
                       default='patch', help='版本增加类型 (默认: patch)')
    parser.add_argument('--no-clean', action='store_true', help='不清理构建目录')
    parser.add_argument('--no-backup', action='store_true', help='不备份旧版本')
    parser.add_argument('--no-version-bump', action='store_true', help='不增加版本号')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("MSBuild工具自动构建脚本")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n依赖项检查失败，构建终止")
        sys.exit(1)
    
    # 版本管理
    vm = VersionManager()
    
    if not args.no_version_bump:
        # 显示当前版本
        current_version = vm.get_version_string()
        print(f"\n当前版本: {current_version}")
        
        # 增加版本号
        major, minor, patch = vm.increment_version(args.type)
        new_version = f"{major}.{minor}.{patch}"
        print(f"新版本: {new_version}")
        
        # 更新源文件
        print("更新源文件中的版本信息...")
        vm.update_source_files(new_version)
    else:
        new_version = vm.get_version_string()
        print(f"使用当前版本: {new_version}")
    
    # 备份旧版本
    if not args.no_backup:
        backup_old_version()
    
    # 清理构建目录
    if not args.no_clean:
        clean_build_dirs()
    
    # 执行构建
    print(f"\n开始构建版本 {new_version}...")
    success = run_command("pyinstaller build.spec", "执行PyInstaller构建")
    
    if success:
        print(f"\n✓ 构建成功! 版本: {new_version}")
        print(f"✓ 构建次数: {vm.get_build_count()}")
        
        # 显示输出文件
        dist_dir = "dist"
        if os.path.exists(dist_dir):
            print(f"\n输出文件位于: {os.path.abspath(dist_dir)}")
            for file in os.listdir(dist_dir):
                if file.endswith('.exe'):
                    file_path = os.path.join(dist_dir, file)
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                    print(f"  - {file} ({file_size:.1f} MB)")
    else:
        print(f"\n✗ 构建失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
