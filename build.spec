# -*- mode: python ; coding: utf-8 -*-

import os
import re
from datetime import datetime
from PyInstaller.utils.win32.versioninfo import (
    FixedFileInfo,
    StringFileInfo,
    StringTable,
    StringStruct,
    VarFileInfo,
    VarStruct,
    VSVersionInfo,
)

block_cipher = None

def get_version_from_source():
    """从源代码中获取版本号"""
    try:
        with open('msbuild.py', 'r', encoding='utf-8') as f:
            content = f.read()
            match = re.search(r'VERSION\s*=\s*["\']([^"\']+)["\']', content)
            if match:
                return match.group(1)
    except FileNotFoundError:
        pass
    return "1.0.0"  # 默认版本

def parse_version(version_str):
    """解析版本字符串为数字元组"""
    try:
        parts = version_str.split('.')
        # 确保至少有4个部分
        while len(parts) < 4:
            parts.append('0')
        return tuple(int(part) for part in parts[:4])  # 最多4个部分
    except:
        return (1, 0, 0, 0)

# 版本信息 - 自动从源代码获取
VERSION = get_version_from_source()
VERSION_TUPLE = parse_version(VERSION)
COMPANY_NAME = "MSBuild Tool"
PRODUCT_NAME = f"MSBuild快速编译工具 v{VERSION}"
FILE_DESCRIPTION = "MSBuild项目快速编译工具"
INTERNAL_NAME = "YGameBuildTool"
ORIGINAL_FILENAME = f"YGameBuildTool_v{VERSION}.exe"
YEAR = datetime.now().year

print(f"构建版本: {VERSION}")
print(f"版本元组: {VERSION_TUPLE}")
print(f"输出文件: {ORIGINAL_FILENAME}")

version_info = VSVersionInfo(
    ffi=FixedFileInfo(
        filevers=VERSION_TUPLE,
        prodvers=VERSION_TUPLE,
        mask=0x3f,
        flags=0x0,
        OS=0x40004,
        fileType=0x1,
        subtype=0x0,
        date=(0, 0)
    ),
    kids=[
        StringFileInfo([
            StringTable(
                "080404b0",
                [
                    StringStruct("CompanyName", COMPANY_NAME),
                    StringStruct("FileDescription", FILE_DESCRIPTION),
                    StringStruct("FileVersion", VERSION),
                    StringStruct("InternalName", INTERNAL_NAME),
                    StringStruct("LegalCopyright", f"Copyright (C) {YEAR}"),
                    StringStruct("OriginalFilename", ORIGINAL_FILENAME),
                    StringStruct("ProductName", PRODUCT_NAME),
                    StringStruct("ProductVersion", VERSION)
                ])
        ]),
        VarFileInfo([VarStruct("Translation", [2052, 1200])])
    ]
)

a = Analysis(
    ['msbuild.py'],
    pathex=[],
    binaries=[],
    datas=[], 
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=ORIGINAL_FILENAME.replace('.exe', ''),
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version=version_info,
) 