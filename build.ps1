# MSBuild工具自动构建脚本 (PowerShell版本)
# 使用方法: .\build.ps1

Write-Host "========================================" -ForegroundColor Green
Write-Host "MSBuild工具自动构建脚本" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Python未找到"
    }
    Write-Host "✓ Python已安装: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 错误: Python未安装或不在PATH中" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 显示菜单
do {
    Write-Host ""
    Write-Host "请选择构建类型:" -ForegroundColor Yellow
    Write-Host "1. Patch版本 (x.x.X+1) - 默认" -ForegroundColor White
    Write-Host "2. Minor版本 (x.X+1.0)" -ForegroundColor White
    Write-Host "3. Major版本 (X+1.0.0)" -ForegroundColor White
    Write-Host "4. 不增加版本号，直接构建" -ForegroundColor White
    Write-Host "5. 仅显示当前版本" -ForegroundColor White
    Write-Host "6. 退出" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "请输入选择 (1-6)"
    
    switch ($choice) {
        "1" {
            Write-Host "执行Patch版本构建..." -ForegroundColor Cyan
            python auto_build.py --type patch
            break
        }
        "2" {
            Write-Host "执行Minor版本构建..." -ForegroundColor Cyan
            python auto_build.py --type minor
            break
        }
        "3" {
            Write-Host "执行Major版本构建..." -ForegroundColor Cyan
            python auto_build.py --type major
            break
        }
        "4" {
            Write-Host "执行直接构建..." -ForegroundColor Cyan
            python auto_build.py --no-version-bump
            break
        }
        "5" {
            Write-Host "当前版本信息:" -ForegroundColor Cyan
            python version_manager.py --show
            continue
        }
        "6" {
            Write-Host "退出构建脚本" -ForegroundColor Yellow
            exit 0
        }
        default {
            Write-Host "无效选择，请重新输入" -ForegroundColor Red
            continue
        }
    }
} while ($true)

Write-Host ""
Write-Host "构建完成!" -ForegroundColor Green
Read-Host "按任意键退出"
