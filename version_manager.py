#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本管理工具
自动管理项目版本号，支持语义化版本控制
"""

import os
import re
import json
import argparse
from datetime import datetime
from typing import Tuple, Optional


class VersionManager:
    """版本管理器"""
    
    def __init__(self, version_file: str = "version.json"):
        self.version_file = version_file
        self.version_pattern = re.compile(r'(\d+)\.(\d+)\.(\d+)')
        
    def load_version(self) -> Tuple[int, int, int]:
        """加载当前版本号"""
        if os.path.exists(self.version_file):
            try:
                with open(self.version_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return (data['major'], data['minor'], data['patch'])
            except (json.JSONDecodeError, KeyError, FileNotFoundError):
                pass
        
        # 如果版本文件不存在，尝试从msbuild.py读取
        return self.extract_version_from_source()
    
    def extract_version_from_source(self) -> Tuple[int, int, int]:
        """从源代码中提取版本号"""
        try:
            with open('msbuild.py', 'r', encoding='utf-8') as f:
                content = f.read()
                match = re.search(r'VERSION\s*=\s*["\'](\d+\.\d+\.\d+)["\']', content)
                if match:
                    version_str = match.group(1)
                    major, minor, patch = map(int, version_str.split('.'))
                    return (major, minor, patch)
        except FileNotFoundError:
            pass
        
        # 默认版本
        return (1, 0, 0)
    
    def save_version(self, major: int, minor: int, patch: int):
        """保存版本信息"""
        version_data = {
            'major': major,
            'minor': minor,
            'patch': patch,
            'version_string': f"{major}.{minor}.{patch}",
            'last_updated': datetime.now().isoformat(),
            'build_count': self.get_build_count() + 1
        }
        
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, indent=4, ensure_ascii=False)
    
    def get_build_count(self) -> int:
        """获取构建次数"""
        if os.path.exists(self.version_file):
            try:
                with open(self.version_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('build_count', 0)
            except:
                pass
        return 0
    
    def increment_version(self, version_type: str = 'patch') -> Tuple[int, int, int]:
        """增加版本号"""
        major, minor, patch = self.load_version()
        
        if version_type == 'major':
            major += 1
            minor = 0
            patch = 0
        elif version_type == 'minor':
            minor += 1
            patch = 0
        elif version_type == 'patch':
            patch += 1
        else:
            raise ValueError(f"不支持的版本类型: {version_type}")
        
        self.save_version(major, minor, patch)
        return (major, minor, patch)
    
    def get_version_string(self) -> str:
        """获取版本字符串"""
        major, minor, patch = self.load_version()
        return f"{major}.{minor}.{patch}"
    
    def update_source_files(self, version_string: str):
        """更新源文件中的版本号"""
        # 更新 msbuild.py
        self.update_file_version('msbuild.py', version_string, [
            (r'VERSION\s*=\s*["\'][^"\']*["\']', f'VERSION = "{version_string}"'),
            (r'"date":\s*"[^"]*"', f'"date": "{datetime.now().strftime("%Y-%m-%d")}"')
        ])
        
        # 更新 build.spec
        self.update_file_version('build.spec', version_string, [
            (r'VERSION\s*=\s*["\'][^"\']*["\']', f'VERSION = "{version_string}"'),
            (r'filevers=\(\d+,\s*\d+,\s*\d+,\s*\d+\)', self.get_file_version_tuple(version_string)),
            (r'prodvers=\(\d+,\s*\d+,\s*\d+,\s*\d+\)', self.get_file_version_tuple(version_string))
        ])
    
    def update_file_version(self, filename: str, version_string: str, patterns: list):
        """更新文件中的版本信息"""
        if not os.path.exists(filename):
            print(f"警告: 文件 {filename} 不存在")
            return
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"已更新 {filename} 中的版本信息")
        except Exception as e:
            print(f"更新 {filename} 时出错: {e}")
    
    def get_file_version_tuple(self, version_string: str) -> str:
        """获取文件版本元组字符串"""
        major, minor, patch = map(int, version_string.split('.'))
        return f"filevers=({major}, {minor}, {patch}, 0)"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='版本管理工具')
    parser.add_argument('--type', choices=['major', 'minor', 'patch'], 
                       default='patch', help='版本增加类型 (默认: patch)')
    parser.add_argument('--show', action='store_true', help='显示当前版本')
    parser.add_argument('--build', action='store_true', help='执行构建')
    
    args = parser.parse_args()
    
    vm = VersionManager()
    
    if args.show:
        print(f"当前版本: {vm.get_version_string()}")
        print(f"构建次数: {vm.get_build_count()}")
        return
    
    # 增加版本号
    major, minor, patch = vm.increment_version(args.type)
    new_version = f"{major}.{minor}.{patch}"
    
    print(f"版本号已更新: {new_version}")
    print(f"构建次数: {vm.get_build_count()}")
    
    # 更新源文件
    vm.update_source_files(new_version)
    
    # 如果指定了构建，则执行PyInstaller
    if args.build:
        print("开始构建...")
        os.system("pyinstaller build.spec")
        print("构建完成!")


if __name__ == "__main__":
    main()
