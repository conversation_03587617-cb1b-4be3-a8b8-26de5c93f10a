# 自动版本管理系统

这个系统可以自动管理项目版本号，无需手动修改代码中的版本信息。

## 文件说明

- `version_manager.py` - 版本管理核心模块
- `auto_build.py` - 自动构建脚本
- `build.bat` - Windows批处理文件（图形化菜单）
- `version.json` - 版本信息存储文件（自动生成）

## 使用方法

### 方法1: 使用批处理文件（推荐）

双击 `build.bat` 文件，选择相应的构建类型：

1. **Patch版本** (1.0.1 → 1.0.2) - 用于bug修复
2. **Minor版本** (1.0.1 → 1.1.0) - 用于新功能
3. **Major版本** (1.0.1 → 2.0.0) - 用于重大更新
4. **不增加版本号** - 直接构建当前版本
5. **显示当前版本** - 查看版本信息

### 方法2: 使用命令行

```bash
# 增加patch版本并构建
python auto_build.py

# 增加minor版本并构建
python auto_build.py --type minor

# 增加major版本并构建
python auto_build.py --type major

# 不增加版本号，直接构建
python auto_build.py --no-version-bump

# 查看当前版本
python version_manager.py --show
```

### 方法3: 仅管理版本号

```bash
# 仅增加版本号，不构建
python version_manager.py --type patch

# 增加minor版本号
python version_manager.py --type minor

# 增加major版本号
python version_manager.py --type major
```

## 版本号规则

采用语义化版本控制 (Semantic Versioning)：

- **Major版本** (X.0.0): 不兼容的API修改
- **Minor版本** (X.Y.0): 向后兼容的功能性新增
- **Patch版本** (X.Y.Z): 向后兼容的问题修正

## 自动更新的文件

系统会自动更新以下文件中的版本信息：

1. `msbuild.py` - 主程序中的VERSION常量
2. `build.spec` - PyInstaller配置文件中的版本信息
3. `version.json` - 版本历史记录

## 版本信息存储

`version.json` 文件包含：

```json
{
    "major": 1,
    "minor": 0,
    "patch": 2,
    "version_string": "1.0.2",
    "last_updated": "2024-03-14T10:30:00",
    "build_count": 15
}
```

## 构建输出

构建完成后，可执行文件会保存在 `dist` 目录中，文件名格式为：
`MSBuildTool_v{版本号}.exe`

例如：`MSBuildTool_v1.0.2.exe`

## 备份机制

每次构建前，系统会自动备份旧版本到以时间戳命名的目录中，例如：
`backup_20240314_103000`

## 注意事项

1. 首次使用时，系统会从 `msbuild.py` 中读取当前版本号
2. 版本信息会同步更新到所有相关文件中
3. 构建前会自动清理 `build` 目录
4. 如果构建失败，版本号不会回滚，需要手动处理

## 故障排除

### 问题：Python未找到
**解决方案：** 确保Python已安装并添加到系统PATH中

### 问题：PyInstaller未安装
**解决方案：** 运行 `pip install pyinstaller`

### 问题：版本号不同步
**解决方案：** 删除 `version.json` 文件，系统会重新从源代码读取版本号

### 问题：构建失败
**解决方案：** 检查 `msbuild.py` 和 `build.spec` 文件是否存在语法错误
